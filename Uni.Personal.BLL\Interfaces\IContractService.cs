using UNI.Model;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Interfaces
{
    /// <summary>
    /// Contract service interface
    /// </summary>
    public interface IContractService
    {
        /// <summary>
        /// Get paginated list of contracts
        /// </summary>
        /// <param name="query">Filter input for contracts</param>
        /// <returns>Paginated list of contracts</returns>
        Task<CommonListPage> GetPageAsync(ContractFilterInput? query);

        /// <summary>
        /// Get contract information by ID
        /// </summary>
        /// <param name="oid">Contract ID</param>
        /// <returns>Contract information</returns>
        Task<CommonViewOidInfo> GetInfoAsync(Guid? oid);

        /// <summary>
        /// Create or update contract
        /// </summary>
        /// <param name="info">Contract information</param>
        /// <returns>Validation result with contract ID</returns>
        Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info);

        /// <summary>
        /// Delete contract
        /// </summary>
        /// <param name="oid">Contract ID</param>
        /// <returns>Validation result</returns>
        Task<BaseValidate> DeleteAsync(Guid? oid);

        /// <summary>
        /// Get detailed contract information
        /// </summary>
        /// <param name="oid">Contract ID</param>
        /// <returns>Detailed contract information</returns>
        Task<ContractDetailView?> GetContractDetailAsync(Guid? oid);
    }
}
