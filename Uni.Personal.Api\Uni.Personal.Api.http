@Uni.Personal.Api_HostAddress = http://localhost:5255
@jwt_token = your_jwt_token_here

# Test Order endpoints without authentication (should return 401)
GET {{Uni.Personal.Api_HostAddress}}/v1/api/Order/GetPage
Accept: application/json

###

# Test Order endpoints with authentication
GET {{Uni.Personal.Api_HostAddress}}/v1/api/Order/GetPage?PageIndex=1&PageSize=10
Accept: application/json
Authorization: Bearer {{jwt_token}}

###

# Get specific order info
GET {{Uni.Personal.Api_HostAddress}}/v1/api/Order/GetInfo?oid=00000000-0000-0000-0000-000000000000
Accept: application/json
Authorization: Bearer {{jwt_token}}

###

# Test Contract endpoints
GET {{Uni.Personal.Api_HostAddress}}/v1/api/Contract/GetPage?PageIndex=1&PageSize=10
Accept: application/json
Authorization: Bearer {{jwt_token}}

###

# Get specific contract info
GET {{Uni.Personal.Api_HostAddress}}/v1/api/Contract/GetInfo?oid=00000000-0000-0000-0000-000000000000
Accept: application/json
Authorization: Bearer {{jwt_token}}

###

# Get contract detail
GET {{Uni.Personal.Api_HostAddress}}/v1/api/Contract/GetContractDetail?oid=00000000-0000-0000-0000-000000000000
Accept: application/json
Authorization: Bearer {{jwt_token}}

###

# Create new contract
POST {{Uni.Personal.Api_HostAddress}}/v1/api/Contract/SetInfo
Accept: application/json
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "OrderId": "00000000-0000-0000-0000-000000000000",
  "OrderDetailsId": "00000000-0000-0000-0000-000000000000",
  "ContractNo": "CT001",
  "ContractDate": "2025-07-08T00:00:00",
  "Status": 1,
  "ContractLink": "https://example.com/contract.pdf"
}

###

# Delete contract
DELETE {{Uni.Personal.Api_HostAddress}}/v1/api/Contract/Delete?oid=00000000-0000-0000-0000-000000000000
Accept: application/json
Authorization: Bearer {{jwt_token}}

###

# Create/Update order
POST {{Uni.Personal.Api_HostAddress}}/v1/api/Order/SetInfo
Accept: application/json
Authorization: Bearer {{jwt_token}}
Content-Type: application/json

{
  "oid": null,
  "data": {
    "orderName": "Test Order",
    "description": "Test order description"
  }
}

###

# Delete order
DELETE {{Uni.Personal.Api_HostAddress}}/v1/api/Order/Delete?oid=00000000-0000-0000-0000-000000000000
Accept: application/json
Authorization: Bearer {{jwt_token}}

###
