using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Uni.Personal.Api;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.Model;
using UNI.Model;
using UNI.Model.Api;
using Xunit;

namespace Uni.Personal.Tests
{
    /// <summary>
    /// Integration tests for Order Management API
    /// </summary>
    public class OrderManagementTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public OrderManagementTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task GetPage_ShouldReturnOrderList()
        {
            // Arrange
            var query = new OrderFilterInput
            {
                PageIndex = 1,
                PageSize = 10
            };

            // Act
            var response = await _client.GetAsync($"/api/v1/Order/GetPage?PageIndex={query.PageIndex}&PageSize={query.PageSize}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<BaseResponse<CommonListPage>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Assert.NotNull(result);
            Assert.Equal(ApiResult.Success, result.Result);
            Assert.NotNull(result.Data);
        }

        [Fact]
        public async Task GetInfo_WithValidId_ShouldReturnOrderInfo()
        {
            // Arrange
            var orderId = Guid.NewGuid(); // In real test, use existing order ID

            // Act
            var response = await _client.GetAsync($"/api/v1/Order/GetInfo?oid={orderId}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<BaseResponse<CommonViewOidInfo>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Assert.NotNull(result);
        }

        [Fact]
        public async Task GetOrderDetail_WithValidId_ShouldReturnOrderDetail()
        {
            // Arrange
            var orderId = Guid.NewGuid(); // In real test, use existing order ID

            // Act
            var response = await _client.GetAsync($"/api/v1/Order/GetOrderDetail?oid={orderId}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<BaseResponse<OrderDetailView>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Assert.NotNull(result);
        }

        [Fact]
        public async Task SetInfo_WithValidData_ShouldCreateOrder()
        {
            // Arrange
            var orderInfo = new OrderInfo
            {
                OrderCode = "TEST" + DateTime.Now.Ticks,
                ContractCode = "CONTRACT" + DateTime.Now.Ticks,
                CustomerName = "Test Customer",
                CustomerEmail = "<EMAIL>",
                CustomerPhone = "0123456789",
                SubTotal = 450000,
                VatRate = 10,
                VatAmount = 45000,
                TotalAmount = 495000,
                Status = 1,
                OrderDate = DateTime.Now,
                Description = "Test order",
                OrderItems = new List<OrderItemInfo>
                {
                    new OrderItemInfo
                    {
                        ItemNo = 1,
                        ProductType = "Usee Apps",
                        ProductName = "Usee Apps - gói Basic",
                        ProductDescription = "Test product",
                        Quantity = 1,
                        Unit = "năm",
                        UnitPrice = 450000,
                        TotalPrice = 450000,
                        Duration = 1,
                        DurationUnit = "year"
                    }
                }
            };

            var json = JsonSerializer.Serialize(orderInfo);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            // Act
            var response = await _client.PostAsync("/api/v1/Order/SetInfo", content);

            // Assert
            response.EnsureSuccessStatusCode();
            var responseContent = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<BaseResponse<BaseValidate<Guid?>>>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Assert.NotNull(result);
            Assert.Equal(ApiResult.Success, result.Result);
        }

        [Fact]
        public async Task UpdateStatus_WithValidData_ShouldUpdateOrderStatus()
        {
            // Arrange
            var orderId = Guid.NewGuid(); // In real test, use existing order ID
            var newStatus = OrderStatus.Paid;

            // Act
            var response = await _client.PutAsync($"/api/v1/Order/UpdateStatus?oid={orderId}&status={newStatus}", null);

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<BaseResponse<BaseValidate>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Assert.NotNull(result);
        }

        [Fact]
        public async Task Delete_WithValidId_ShouldDeleteOrder()
        {
            // Arrange
            var orderId = Guid.NewGuid(); // In real test, use existing order ID

            // Act
            var response = await _client.DeleteAsync($"/api/v1/Order/Delete?oid={orderId}");

            // Assert
            response.EnsureSuccessStatusCode();
            var content = await response.Content.ReadAsStringAsync();
            var result = JsonSerializer.Deserialize<BaseResponse<BaseValidate>>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Assert.NotNull(result);
        }
    }

    /// <summary>
    /// Unit tests for Order Service
    /// </summary>
    public class OrderServiceTests
    {
        [Fact]
        public void OrderStatus_ShouldHaveCorrectValues()
        {
            // Assert
            Assert.Equal(0, (int)OrderStatus.Draft);
            Assert.Equal(1, (int)OrderStatus.WaitingForPayment);
            Assert.Equal(2, (int)OrderStatus.Paid);
            Assert.Equal(3, (int)OrderStatus.Processing);
            Assert.Equal(4, (int)OrderStatus.Completed);
            Assert.Equal(5, (int)OrderStatus.Cancelled);
            Assert.Equal(6, (int)OrderStatus.Refunded);
        }

        [Fact]
        public void OrderFilterInput_ShouldInheritFromFilterInput()
        {
            // Arrange & Act
            var filter = new OrderFilterInput();

            // Assert
            Assert.IsAssignableFrom<FilterInput>(filter);
            Assert.NotNull(filter);
        }

        [Fact]
        public void OrderInfo_ShouldInheritFromCommonViewOidInfo()
        {
            // Arrange & Act
            var orderInfo = new OrderInfo();

            // Assert
            Assert.IsAssignableFrom<CommonViewOidInfo>(orderInfo);
            Assert.NotNull(orderInfo);
        }

        [Fact]
        public void OrderDetailView_ShouldHaveOrderItemsList()
        {
            // Arrange & Act
            var orderDetail = new OrderDetailView
            {
                OrderItems = new List<OrderItemView>
                {
                    new OrderItemView
                    {
                        ItemNo = 1,
                        ProductName = "Test Product"
                    }
                }
            };

            // Assert
            Assert.NotNull(orderDetail.OrderItems);
            Assert.Single(orderDetail.OrderItems);
            Assert.Equal(1, orderDetail.OrderItems[0].ItemNo);
            Assert.Equal("Test Product", orderDetail.OrderItems[0].ProductName);
        }
    }

    /// <summary>
    /// Business logic tests for Order Management
    /// </summary>
    public class OrderBusinessLogicTests
    {
        [Theory]
        [InlineData(450000, 10, 45000, 495000)]
        [InlineData(900000, 10, 90000, 990000)]
        [InlineData(1000000, 8, 80000, 1080000)]
        public void CalculateOrderTotal_ShouldReturnCorrectAmount(decimal subTotal, decimal vatRate, decimal expectedVat, decimal expectedTotal)
        {
            // Arrange
            var orderInfo = new OrderInfo
            {
                SubTotal = subTotal,
                VatRate = vatRate
            };

            // Act
            var calculatedVat = Math.Round(subTotal * vatRate / 100, 2);
            var calculatedTotal = subTotal + calculatedVat;

            // Assert
            Assert.Equal(expectedVat, calculatedVat);
            Assert.Equal(expectedTotal, calculatedTotal);
        }

        [Fact]
        public void OrderCode_ShouldBeUnique()
        {
            // This test would typically check database constraints
            // For now, we just verify the property exists
            var order1 = new OrderInfo { OrderCode = "S0949762" };
            var order2 = new OrderInfo { OrderCode = "S0949763" };

            Assert.NotEqual(order1.OrderCode, order2.OrderCode);
        }
    }
}
