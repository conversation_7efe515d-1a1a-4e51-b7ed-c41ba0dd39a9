# Order Management API Examples

## Sample API Calls for Order S0949762

Based on the order details shown in the screenshot, here are example API calls:

### 1. Get Order Detail (Matching Screenshot)

**Request:**
```http
GET /api/v1/Order/GetOrderDetail?oid={order-guid}
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "result": 1,
  "message": "Success",
  "data": {
    "oid": "12345678-1234-1234-1234-123456789012",
    "orderCode": "S0949762",
    "contractCode": "S0949762",
    "orderDate": "2022-11-18T00:00:00",
    "createdDate": "2022-11-18T00:00:00",
    "customerName": "Nguyễn Văn A",
    "customerEmail": "<EMAIL>",
    "customerPhone": "0123456789",
    "status": 1,
    "statusText": "Chờ thanh toán",
    "subTotal": 450000,
    "vatRate": 10,
    "vatAmount": 45000,
    "totalAmount": 495000,
    "description": "Đơn hàng gói Usee Apps Basic",
    "notes": "Khách hàng yêu cầu thanh toán qua chuyển khoản",
    "orderItems": [
      {
        "itemNo": 1,
        "productType": "Usee Apps",
        "productName": "Usee Apps - gói Basic",
        "productDescription": "Gói dịch vụ Usee Apps cơ bản",
        "quantity": 1,
        "unit": "năm",
        "unitPrice": 450000,
        "totalPrice": 450000,
        "duration": 1,
        "durationUnit": "year"
      }
    ]
  }
}
```

### 2. Update Order Status to "Paid"

**Request:**
```http
PUT /api/v1/Order/UpdateStatus?oid={order-guid}&status=2
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "result": 1,
  "message": "Success",
  "data": {
    "valid": true,
    "message": "Order status updated successfully"
  }
}
```

### 3. Get Orders List with Pagination

**Request:**
```http
GET /api/v1/Order/GetPage?PageIndex=1&PageSize=10&Status=1
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "result": 1,
  "message": "Success",
  "data": {
    "items": [
      {
        "oid": "12345678-1234-1234-1234-123456789012",
        "orderCode": "S0949762",
        "contractCode": "S0949762",
        "customerName": "Nguyễn Văn A",
        "customerEmail": "<EMAIL>",
        "productType": "Usee Apps",
        "totalAmount": 495000,
        "status": 1,
        "statusText": "Chờ thanh toán",
        "orderDate": "2022-11-18T00:00:00",
        "createdDate": "2022-11-18T00:00:00",
        "createdBy": "system"
      }
    ],
    "totalCount": 1,
    "pageIndex": 1,
    "pageSize": 10,
    "totalPages": 1
  }
}
```

### 4. Create New Order

**Request:**
```http
POST /api/v1/Order/SetInfo
Authorization: Bearer {jwt-token}
Content-Type: application/json

{
  "orderCode": "S0949764",
  "contractCode": "S0949764",
  "customerName": "Lê Văn C",
  "customerEmail": "<EMAIL>",
  "customerPhone": "0987654321",
  "subTotal": 900000,
  "vatRate": 10,
  "vatAmount": 90000,
  "totalAmount": 990000,
  "status": 1,
  "orderDate": "2024-01-15T00:00:00",
  "description": "Đơn hàng gói Usee Apps Premium",
  "notes": "Khách hàng VIP",
  "orderItems": [
    {
      "itemNo": 1,
      "productType": "Usee Apps",
      "productName": "Usee Apps - gói Premium",
      "productDescription": "Gói dịch vụ Usee Apps cao cấp",
      "quantity": 1,
      "unit": "năm",
      "unitPrice": 900000,
      "totalPrice": 900000,
      "duration": 1,
      "durationUnit": "year"
    }
  ]
}
```

**Response:**
```json
{
  "result": 1,
  "message": "Success",
  "data": {
    "valid": true,
    "message": "Order saved successfully",
    "data": "12345678-1234-1234-1234-123456789013"
  }
}
```

### 5. Search Orders by Customer Name

**Request:**
```http
GET /api/v1/Order/GetPage?PageIndex=1&PageSize=10&CustomerName=Nguyễn
Authorization: Bearer {jwt-token}
```

### 6. Filter Orders by Date Range

**Request:**
```http
GET /api/v1/Order/GetPage?PageIndex=1&PageSize=10&FromDate=2022-11-01&ToDate=2022-11-30
Authorization: Bearer {jwt-token}
```

### 7. Get Order Basic Info

**Request:**
```http
GET /api/v1/Order/GetInfo?oid={order-guid}
Authorization: Bearer {jwt-token}
```

**Response:**
```json
{
  "result": 1,
  "message": "Success",
  "data": {
    "oid": "12345678-1234-1234-1234-123456789012",
    "orderCode": "S0949762",
    "contractCode": "S0949762",
    "customerName": "Nguyễn Văn A",
    "customerEmail": "<EMAIL>",
    "customerPhone": "0123456789",
    "subTotal": 450000,
    "vatRate": 10,
    "vatAmount": 45000,
    "totalAmount": 495000,
    "status": 1,
    "statusText": "Chờ thanh toán",
    "orderDate": "2022-11-18T00:00:00",
    "description": "Đơn hàng gói Usee Apps Basic",
    "notes": "Khách hàng yêu cầu thanh toán qua chuyển khoản",
    "createdBy": "system",
    "createdDate": "2022-11-18T00:00:00",
    "modifiedBy": null,
    "modifiedDate": null
  }
}
```

## Error Responses

### 400 Bad Request
```json
{
  "result": 0,
  "message": "Invalid request data",
  "data": null
}
```

### 404 Not Found
```json
{
  "result": 0,
  "message": "Order not found",
  "data": null
}
```

### 500 Internal Server Error
```json
{
  "result": 0,
  "message": "An error occurred while processing the request",
  "data": null
}
```

## Authentication

All API endpoints require JWT authentication. Include the Bearer token in the Authorization header:

```http
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Rate Limiting

API calls are subject to rate limiting. Standard limits apply per user/client.

## Data Validation

- Order codes must be unique
- Customer name is required
- Amounts must be positive numbers
- Status values must be valid (0-6)
- Email format validation applies where applicable
