using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNI.Model;
using UNI.Model.Api;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.Api.Controllers.Version1
{
    [Route("/api/v1/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class ContractController : UniController
    {
        private readonly IContractService _contractService;

        public ContractController(IContractService contractService)
        {
            _contractService = contractService;
        }

        /// <summary>
        /// Get paginated list of contracts
        /// </summary>
        /// <param name="query">Filter criteria for contracts</param>
        /// <returns>Paginated list of contracts</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetPage([FromQuery] ContractFilterInput? query)
        {
            if (query == null)
            {
                return GetErrorResponse<CommonListPage>(ApiResult.Error, 12, "Invalid request data");
            }
            var result = await _contractService.GetPageAsync(query);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Get contract information by ID
        /// </summary>
        /// <param name="oid">Contract ID</param>
        /// <returns>Contract information</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonViewOidInfo>> GetInfo([FromQuery] Guid? oid)
        {
            if (!oid.HasValue)
            {
                return GetErrorResponse<CommonViewOidInfo>(ApiResult.Error, 12, "Contract ID is required");
            }

            var result = await _contractService.GetInfoAsync(oid);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Create or update contract
        /// </summary>
        /// <param name="info">Contract information</param>
        /// <returns>Validation result with contract ID</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate<Guid?>>> SetInfo([FromBody] CommonViewOidInfo? info)
        {
            if (info == null)
            {
                return GetErrorResponse<BaseValidate<Guid?>>(ApiResult.Error, 12, "Invalid request data");
            }

            var result = await _contractService.SetInfoAsync(info);

            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }

        /// <summary>
        /// Delete contract
        /// </summary>
        /// <param name="oid">Contract ID</param>
        /// <returns>Validation result</returns>
        [HttpDelete]
        public async Task<BaseResponse<BaseValidate>> Delete([FromQuery] Guid? oid)
        {
            if (!oid.HasValue)
            {
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 12, "Contract ID is required");
            }

            var result = await _contractService.DeleteAsync(oid);

            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }

        /// <summary>
        /// Get detailed contract information
        /// </summary>
        /// <param name="oid">Contract ID</param>
        /// <returns>Detailed contract information</returns>
        [HttpGet]
        public async Task<BaseResponse<ContractDetailView?>> GetContractDetail([FromQuery] Guid? oid)
        {
            if (!oid.HasValue)
            {
                return GetErrorResponse<ContractDetailView?>(ApiResult.Error, 12, "Contract ID is required");
            }

            var result = await _contractService.GetContractDetailAsync(oid);
            return GetResponse(ApiResult.Success, result);
        }
    }
}
