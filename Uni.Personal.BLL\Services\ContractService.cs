using UNI.Model;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// Contract service implementation
    /// </summary>
    public class ContractService : IContractService
    {
        private readonly IContractRepository _contractRepository;

        public ContractService(IContractRepository contractRepository)
        {
            _contractRepository = contractRepository;
        }

        /// <summary>
        /// Get paginated list of contracts
        /// </summary>
        /// <param name="query">Filter input for contracts</param>
        /// <returns>Paginated list of contracts</returns>
        public async Task<CommonListPage> GetPageAsync(ContractFilterInput? query)
        {
            return await _contractRepository.GetPageAsync(query);
        }

        /// <summary>
        /// Get contract information by ID
        /// </summary>
        /// <param name="oid">Contract ID</param>
        /// <returns>Contract information</returns>
        public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            return await _contractRepository.GetInfoAsync(oid);
        }

        /// <summary>
        /// Create or update contract
        /// </summary>
        /// <param name="info">Contract information</param>
        /// <returns>Validation result with contract ID</returns>
        public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            // Add business logic validation here if needed
            // For example: check if contract can be created/updated, validate permissions, etc.

            return await _contractRepository.SetInfoAsync(info);
        }

        /// <summary>
        /// Delete contract
        /// </summary>
        /// <param name="oid">Contract ID</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> DeleteAsync(Guid? oid)
        {
            // Add business logic validation here if needed
            // For example: check if contract can be deleted, validate permissions, etc.

            return await _contractRepository.DeleteAsync(oid);
        }

        /// <summary>
        /// Get detailed contract information
        /// </summary>
        /// <param name="oid">Contract ID</param>
        /// <returns>Detailed contract information</returns>
        public async Task<ContractDetailView?> GetContractDetailAsync(Guid? oid)
        {
            return await _contractRepository.GetContractDetailAsync(oid);
        }
    }
}
