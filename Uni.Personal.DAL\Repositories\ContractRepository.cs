using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Repositories
{
    /// <summary>
    /// Contract repository implementation
    /// </summary>
    public class ContractRepository : UniBaseRepository, IContractRepository
    {
        public ContractRepository(IUniCommonBaseRepository commonBase) : base(commonBase)
        {
        }

        /// <summary>
        /// Get paginated list of contracts
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated list of contracts</returns>
        public async Task<CommonListPage> GetPageAsync(ContractFilterInput filter)
        {
            // Call stored procedure to get paginated contracts
            return await GetPageAsync("sp_personal_contract_page", filter, param =>
            {
                // Add custom parameters if needed
                param.Add("@ContractNo", filter.ContractNo);
                param.Add("@OrderCode", filter.OrderCode);
                param.Add("@CustomerName", filter.CustomerName);
                param.Add("@FromDate", filter.FromDate);
                param.Add("@ToDate", filter.ToDate);
                param.Add("@Status", filter.Status);
                return param;
            });
        }

        /// <summary>
        /// Get contract information by ID
        /// </summary>
        /// <param name="oid">Contract ID</param>
        /// <returns>Contract information</returns>
        public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            // Call stored procedure to get contract info
            return await GetFirstOrDefaultAsync<CommonViewOidInfo>("sp_personal_contract_GetInfo", null, new { Oid = oid });
        }

        /// <summary>
        /// Create or update contract
        /// </summary>
        /// <param name="info">Contract information</param>
        /// <returns>Validation result with contract ID</returns>
        public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            // Call stored procedure to create/update contract
            return await SetAsync<Guid?>("sp_personal_contract_SetInfo", info.ToObject());
        }

        /// <summary>
        /// Delete contract
        /// </summary>
        /// <param name="oid">Contract ID</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> DeleteAsync(Guid? oid)
        {
            // Call stored procedure to delete contract
            return await SetAsync("sp_personal_contract_Delete", new { Oid = oid });
        }

        /// <summary>
        /// Get detailed contract information
        /// </summary>
        /// <param name="oid">Contract ID</param>
        /// <returns>Detailed contract information</returns>
        public async Task<ContractDetailView?> GetContractDetailAsync(Guid? oid)
        {
            // Call stored procedure to get detailed contract information
            return await GetFirstOrDefaultAsync<ContractDetailView?>("sp_personal_contract_GetDetail", null, new { Oid = oid });
        }
    }
}
