# Order Management Backend Documentation

## Overview

This document describes the backend implementation for the Order Management system in the Uni Personal Portal. The system handles order creation, management, and tracking with support for multiple order items, VAT calculations, and status management.

## Architecture

The system follows a layered architecture pattern:

- **API Layer**: `Uni.Personal.Api/Controllers/Version1/OrderController.cs`
- **Business Logic Layer**: `Uni.Personal.BLL/Services/OrderService.cs`
- **Data Access Layer**: `Uni.Personal.DAL/Repositories/OrderRepository.cs`
- **Models**: `Uni.Personal.Model/OrderModels.cs`

## Database Schema

### Tables

#### Orders
Main order table containing order header information:
- `Oid` (UNIQUEIDENTIFIER): Primary key
- `OrderCode` (NVARCHAR(50)): Unique order code
- `ContractCode` (NVARCHAR(50)): Associated contract code
- `CustomerName` (NVARCHAR(255)): Customer name
- `CustomerEmail` (NVARCHAR(255)): Customer email
- `CustomerPhone` (NVARCHAR(50)): Customer phone
- `SubTotal` (DECIMAL(18,2)): Subtotal amount
- `VatRate` (DECIMAL(5,2)): VAT rate percentage
- `VatAmount` (DECIMAL(18,2)): VAT amount
- `TotalAmount` (DECIMAL(18,2)): Total amount including VAT
- `Status` (INT): Order status (0-6)
- `OrderDate` (DATETIME): Order date
- `Description` (NVARCHAR(MAX)): Order description
- `Notes` (NVARCHAR(MAX)): Additional notes

#### OrderItems
Order line items table:
- `Oid` (UNIQUEIDENTIFIER): Primary key
- `OrderOid` (UNIQUEIDENTIFIER): Foreign key to Orders table
- `ItemNo` (INT): Item sequence number
- `ProductType` (NVARCHAR(100)): Product type
- `ProductName` (NVARCHAR(255)): Product name
- `ProductDescription` (NVARCHAR(MAX)): Product description
- `Quantity` (INT): Quantity
- `Unit` (NVARCHAR(50)): Unit of measure
- `UnitPrice` (DECIMAL(18,2)): Unit price
- `TotalPrice` (DECIMAL(18,2)): Total price for this item
- `Duration` (INT): Service duration
- `DurationUnit` (NVARCHAR(20)): Duration unit (day, month, year)

## Order Status Values

| Value | Status | Description |
|-------|--------|-------------|
| 0 | Draft | Order is being created |
| 1 | WaitingForPayment | Order is waiting for payment |
| 2 | Paid | Order has been paid |
| 3 | Processing | Order is being processed |
| 4 | Completed | Order has been completed |
| 5 | Cancelled | Order has been cancelled |
| 6 | Refunded | Order has been refunded |

## API Endpoints

### GET /api/v1/Order/GetPage
Get paginated list of orders.

**Parameters:**
- `PageIndex` (int): Page number (default: 1)
- `PageSize` (int): Page size (default: 10)
- `OrderCode` (string): Filter by order code
- `ContractCode` (string): Filter by contract code
- `CustomerName` (string): Filter by customer name
- `FromDate` (DateTime): Filter from date
- `ToDate` (DateTime): Filter to date
- `Status` (int): Filter by status
- `ProductType` (string): Filter by product type

**Response:**
```json
{
  "result": 1,
  "data": {
    "items": [...],
    "totalCount": 100,
    "pageIndex": 1,
    "pageSize": 10
  }
}
```

### GET /api/v1/Order/GetInfo
Get order information by ID.

**Parameters:**
- `oid` (Guid): Order ID

**Response:**
```json
{
  "result": 1,
  "data": {
    "oid": "...",
    "orderCode": "S0949762",
    "contractCode": "S0949762",
    "customerName": "Nguyễn Văn A",
    "totalAmount": 495000,
    "status": 1
  }
}
```

### GET /api/v1/Order/GetOrderDetail
Get detailed order information including items.

**Parameters:**
- `oid` (Guid): Order ID

**Response:**
```json
{
  "result": 1,
  "data": {
    "oid": "...",
    "orderCode": "S0949762",
    "customerName": "Nguyễn Văn A",
    "subTotal": 450000,
    "vatAmount": 45000,
    "totalAmount": 495000,
    "orderItems": [
      {
        "itemNo": 1,
        "productName": "Usee Apps - gói Basic",
        "quantity": 1,
        "unitPrice": 450000,
        "totalPrice": 450000
      }
    ]
  }
}
```

### POST /api/v1/Order/SetInfo
Create or update order.

**Request Body:**
```json
{
  "oid": null,
  "orderCode": "S0949762",
  "contractCode": "S0949762",
  "customerName": "Nguyễn Văn A",
  "customerEmail": "<EMAIL>",
  "subTotal": 450000,
  "vatRate": 10,
  "vatAmount": 45000,
  "totalAmount": 495000,
  "status": 1,
  "orderItems": [...]
}
```

### PUT /api/v1/Order/UpdateStatus
Update order status.

**Parameters:**
- `oid` (Guid): Order ID
- `status` (OrderStatus): New status

### DELETE /api/v1/Order/Delete
Delete order.

**Parameters:**
- `oid` (Guid): Order ID

## Stored Procedures

- `sp_personal_order_page`: Get paginated orders
- `sp_personal_order_GetInfo`: Get order information
- `sp_personal_order_GetDetail`: Get detailed order with items
- `sp_personal_order_SetInfo`: Create or update order
- `sp_personal_order_Delete`: Delete order
- `sp_personal_order_UpdateStatus`: Update order status

## Business Rules

1. **Order Code Uniqueness**: Each order must have a unique order code
2. **Status Transitions**: Orders can only transition to valid next states
3. **Deletion Rules**: Orders with status Paid, Processing, or Completed cannot be deleted
4. **VAT Calculation**: VAT is calculated as SubTotal * VatRate / 100
5. **Total Calculation**: TotalAmount = SubTotal + VatAmount

## Error Handling

The system uses the standard `BaseValidate` response pattern:
- `valid` (bool): Indicates if the operation was successful
- `message` (string): Error or success message
- `data` (object): Additional data (for successful operations)

## Testing

Unit and integration tests are provided in `Tests/OrderManagementTests.cs`:
- API endpoint tests
- Business logic tests
- Model validation tests

## Dependencies

- **NetCore.AutoRegisterDi**: For automatic dependency injection
- **Microsoft.AspNetCore.Authentication.JwtBearer**: For JWT authentication
- **Serilog**: For logging

## Configuration

Database connection string is configured in `appsettings.json`:
```json
{
  "ConnectionStrings": {
    "UniPersonalConnection": "Server=...;Database=dbUniMaster;..."
  }
}
```

## Security

- All endpoints require JWT authentication
- Authorization is handled through the `[Authorize]` attribute
- Input validation is performed at the API and business logic layers
