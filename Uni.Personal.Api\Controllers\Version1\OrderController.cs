using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using UNI.Model;
using UNI.Model.Api;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.Api.Controllers.Version1
{
    [Route("/api/v1/[controller]/[action]")]
    [ApiController]
    [Authorize]
    public class OrderController : UniController
    {
        private readonly IOrderService _orderService;

        public OrderController(IOrderService orderService)
        {
            _orderService = orderService;
        }

        /// <summary>
        /// Get paginated list of orders
        /// </summary>
        /// <param name="query">Filter criteria for orders</param>
        /// <returns>Paginated list of orders</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonListPage>> GetPage([FromQuery] OrderFilterInput? query)
        {
            if (query == null)
            {
                return GetErrorResponse<CommonListPage>(ApiResult.Error, 12, "Invalid request data");
            }
            var result = await _orderService.GetPageAsync(query);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Get order information by ID
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Order information</returns>
        [HttpGet]
        public async Task<BaseResponse<CommonViewOidInfo>> GetInfo([FromQuery] Guid? oid)
        {
            if (!oid.HasValue)
            {
                return GetErrorResponse<CommonViewOidInfo>(ApiResult.Error, 12, "Order ID is required");
            }

            var result = await _orderService.GetInfoAsync(oid);
            return GetResponse(ApiResult.Success, result);
        }

        /// <summary>
        /// Create or update order
        /// </summary>
        /// <param name="info">Order information</param>
        /// <returns>Validation result with order ID</returns>
        [HttpPost]
        public async Task<BaseResponse<BaseValidate<Guid?>>> SetInfo([FromBody] CommonViewOidInfo? info)
        {
            if (info == null)
            {
                return GetErrorResponse<BaseValidate<Guid?>>(ApiResult.Error, 12, "Invalid request data");
            }

            var result = await _orderService.SetInfoAsync(info);

            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }

        /// <summary>
        /// Delete order
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Validation result</returns>
        [HttpDelete]
        public async Task<BaseResponse<BaseValidate>> Delete([FromQuery] Guid? oid)
        {
            if (!oid.HasValue)
            {
                return GetErrorResponse<BaseValidate>(ApiResult.Error, 12, "Order ID is required");
            }

            var result = await _orderService.DeleteAsync(oid);

            return result.valid ? GetResponse(ApiResult.Success, result) : GetResponse(ApiResult.Error, result, result.messages);
        }

        /// <summary>
        /// Get detailed order information including items
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Detailed order information</returns>
        [HttpGet]
        public async Task<BaseResponse<OrderDetailView?>> GetOrderDetail([FromQuery] Guid? oid)
        {
            if (!oid.HasValue)
            {
                return GetErrorResponse<OrderDetailView?>(ApiResult.Error, 12, "Order ID is required");
            }

            var result = await _orderService.GetOrderDetailAsync(oid);
            return GetResponse(ApiResult.Success, result);
        }
    }
}
